/* Styles pour la carte interactive de Tunisie */

.tunisia-map-section {
    background-color: #f8f9fa;
}

.city-filters {
    margin-bottom: 2rem;
}

.city-filters .filter-btn {
    margin: 0 5px 10px 5px;
    border-radius: 25px;
    padding: 8px 20px;
    transition: all 0.3s ease;
    border: 2px solid #f69314;
    color: #f69314;
    background-color: transparent;
    font-weight: 500;
}

.city-filters .filter-btn.active {
    background-color: #f69314;
    border-color: #f69314;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(246, 147, 20, 0.3);
}

.city-filters .filter-btn:hover {
    background-color: #f69314;
    border-color: #f69314;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(246, 147, 20, 0.3);
}

.city-filters .filter-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(246, 147, 20, 0.2);
}

#tunisia-map {
    height: 600px;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: none;
    overflow: hidden;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    position: relative;
}

/* Masquer les contrôles de zoom par défaut */
.leaflet-control-zoom {
    display: none;
}

/* Masquer l'attribution par défaut */
.leaflet-control-attribution {
    display: none;
}

/* Styles pour les gouvernorats */
.leaflet-interactive {
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    stroke-linejoin: round;
    stroke-linecap: round;
}

.leaflet-interactive:hover {
    stroke-width: 3;
    stroke-opacity: 1;
    filter: brightness(1.1);
    transform: scale(1.02);
    transform-origin: center;
}

/* Effet de sélection */
.governorate-selected {
    stroke-width: 4 !important;
    stroke: #ffffff !important;
    stroke-opacity: 1 !important;
    filter: brightness(1.2) !important;
    z-index: 1000 !important;
}

.city-info-panel {
    margin-top: 2rem;
    animation: fadeInUp 0.5s ease-out;
}

.city-info-panel .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.city-info-panel .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.city-info-panel .card-body {
    padding: 2rem;
}

.info-item {
    padding: 20px 10px;
    transition: transform 0.3s ease;
}

.info-item:hover {
    transform: translateY(-3px);
}

.info-item .custom-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
    color: #f69314;
    transition: transform 0.3s ease;
}

.info-item:hover .custom-icon {
    transform: scale(1.1);
}

.info-item h5 {
    color: #343a40;
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 1.1rem;
}

.info-item p {
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0;
}

/* Styles pour les popups Leaflet */
.leaflet-popup-content-wrapper {
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    border: none;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    backdrop-filter: blur(10px);
}

.leaflet-popup-tip {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.leaflet-popup-content {
    margin: 20px;
    text-align: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.leaflet-popup-content h5 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 700;
    font-size: 1.2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.leaflet-popup-content p {
    color: #5a6c7d;
    margin-bottom: 15px;
    line-height: 1.6;
    font-size: 0.9rem;
}

.leaflet-popup-content .btn {
    border-radius: 25px;
    padding: 10px 20px;
    font-size: 0.875rem;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.leaflet-popup-content .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* Styles pour la légende des régions */
.region-legend {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    margin-top: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 8px 0;
    transition: all 0.3s ease;
}

.legend-item:hover {
    transform: translateX(5px);
    background-color: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    padding-left: 10px;
}

.legend-color {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    margin-right: 15px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    position: relative;
}

.legend-color::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
}

.legend-label {
    font-size: 1rem;
    color: #2c3e50;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.legend-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

/* Styles pour les marqueurs personnalisés */
.custom-marker {
    background-color: #f69314;
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.custom-marker:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 15px rgba(246, 147, 20, 0.5);
}

/* Animation pour l'apparition du panneau d'informations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animations personnalisées */
@keyframes mapAppear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes legendSlideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes governorateSelect {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

#tunisia-map {
    animation: mapAppear 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.region-legend {
    animation: legendSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

/* Effet de particules pour le fond */
#tunisia-map::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

/* Responsive design amélioré */
@media (max-width: 768px) {
    #tunisia-map {
        height: 450px;
        border-radius: 10px;
    }

    .city-filters .filter-btn {
        margin: 0 3px 8px 3px;
        padding: 8px 16px;
        font-size: 0.875rem;
    }

    .info-item {
        padding: 15px 5px;
    }

    .info-item .custom-icon {
        font-size: 2rem;
    }

    .city-info-panel .card-body {
        padding: 1.5rem;
    }

    .region-legend {
        margin-top: 20px;
        padding: 20px;
    }

    .legend-title {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    #tunisia-map {
        height: 350px;
        border-radius: 5px;
    }
    
    .city-filters {
        text-align: center;
    }
    
    .city-filters .filter-btn {
        display: block;
        width: 100%;
        margin: 0 0 10px 0;
        max-width: 200px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .info-item h5 {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem !important;
    }
}

/* Amélioration de l'accessibilité */
.filter-btn:focus,
.btn:focus {
    outline: 2px solid #f69314;
    outline-offset: 2px;
}

/* Styles pour les contrôles de zoom Leaflet */
.leaflet-control-zoom a {
    background-color: white;
    border: 1px solid #ccc;
    color: #333;
}

.leaflet-control-zoom a:hover {
    background-color: #f69314;
    color: white;
    border-color: #f69314;
}

/* Style pour le titre de la section */
.tunisia-map-section .section-title {
    color: #343a40;
    font-weight: 700;
    position: relative;
}

.tunisia-map-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #f69314;
    border-radius: 2px;
}

/* Style pour le texte de description */
.tunisia-map-section .lead {
    color: #6c757d;
    font-weight: 400;
    line-height: 1.6;
}
