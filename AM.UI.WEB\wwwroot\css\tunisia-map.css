/* Styles pour la carte interactive de Tunisie */

.tunisia-map-section {
    background-color: #f8f9fa;
}

.city-filters {
    margin-bottom: 2rem;
}

.city-filters .filter-btn {
    margin: 0 5px 10px 5px;
    border-radius: 25px;
    padding: 8px 20px;
    transition: all 0.3s ease;
    border: 2px solid #f69314;
    color: #f69314;
    background-color: transparent;
    font-weight: 500;
}

.city-filters .filter-btn.active {
    background-color: #f69314;
    border-color: #f69314;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(246, 147, 20, 0.3);
}

.city-filters .filter-btn:hover {
    background-color: #f69314;
    border-color: #f69314;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(246, 147, 20, 0.3);
}

.city-filters .filter-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(246, 147, 20, 0.2);
}

#tunisia-map {
    height: 500px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.city-info-panel {
    margin-top: 2rem;
    animation: fadeInUp 0.5s ease-out;
}

.city-info-panel .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.city-info-panel .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.city-info-panel .card-body {
    padding: 2rem;
}

.info-item {
    padding: 20px 10px;
    transition: transform 0.3s ease;
}

.info-item:hover {
    transform: translateY(-3px);
}

.info-item .custom-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
    color: #f69314;
    transition: transform 0.3s ease;
}

.info-item:hover .custom-icon {
    transform: scale(1.1);
}

.info-item h5 {
    color: #343a40;
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 1.1rem;
}

.info-item p {
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0;
}

/* Styles pour les popups Leaflet */
.leaflet-popup-content-wrapper {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.leaflet-popup-content {
    margin: 15px;
    text-align: center;
    font-family: 'Roboto', sans-serif;
}

.leaflet-popup-content h5 {
    color: #f69314;
    margin-bottom: 10px;
    font-weight: 600;
}

.leaflet-popup-content p {
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.5;
}

.leaflet-popup-content .btn {
    border-radius: 20px;
    padding: 6px 15px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Styles pour les marqueurs personnalisés */
.custom-marker {
    background-color: #f69314;
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.custom-marker:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 15px rgba(246, 147, 20, 0.5);
}

/* Animation pour l'apparition du panneau d'informations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    #tunisia-map {
        height: 400px;
    }
    
    .city-filters .filter-btn {
        margin: 0 3px 8px 3px;
        padding: 6px 15px;
        font-size: 0.875rem;
    }
    
    .info-item {
        padding: 15px 5px;
    }
    
    .info-item .custom-icon {
        font-size: 2rem;
    }
    
    .city-info-panel .card-body {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    #tunisia-map {
        height: 350px;
        border-radius: 5px;
    }
    
    .city-filters {
        text-align: center;
    }
    
    .city-filters .filter-btn {
        display: block;
        width: 100%;
        margin: 0 0 10px 0;
        max-width: 200px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .info-item h5 {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem !important;
    }
}

/* Amélioration de l'accessibilité */
.filter-btn:focus,
.btn:focus {
    outline: 2px solid #f69314;
    outline-offset: 2px;
}

/* Styles pour les contrôles de zoom Leaflet */
.leaflet-control-zoom a {
    background-color: white;
    border: 1px solid #ccc;
    color: #333;
}

.leaflet-control-zoom a:hover {
    background-color: #f69314;
    color: white;
    border-color: #f69314;
}

/* Style pour le titre de la section */
.tunisia-map-section .section-title {
    color: #343a40;
    font-weight: 700;
    position: relative;
}

.tunisia-map-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #f69314;
    border-radius: 2px;
}

/* Style pour le texte de description */
.tunisia-map-section .lead {
    color: #6c757d;
    font-weight: 400;
    line-height: 1.6;
}
