/**
 * Script pour la carte interactive de Tunisie
 * Gestion des marqueurs, filtres et interactions utilisateur
 */

document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si Leaflet est chargé
    if (typeof L === 'undefined') {
        console.error('Leaflet n\'est pas chargé. Veuillez vérifier que la bibliothèque Leaflet est incluse.');
        return;
    }

    // Initialisation de la carte
    var map = L.map('tunisia-map', {
        center: [34.0, 9.0],
        zoom: 7,
        zoomControl: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        dragging: true,
        touchZoom: true
    });

    // Ajout de la couche de tuiles avec attribution (style minimal)
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 10,
        minZoom: 6,
        opacity: 0.3
    }).addTo(map);

    // Données des gouvernorats tunisiens avec leurs informations
    var gouvernorats = [
        {
            name: "Tunis",
            region: "nord",
            description: "Capitale de la Tunisie et centre économique principal",
            activeAuctions: "15 enchères",
            upcomingAuctions: "8 prochaines",
            customsOffice: "Bureau principal des douanes",
            population: "1,056,247 habitants",
            chef_lieu: "Tunis"
        },
        {
            name: "Ariana",
            region: "nord",
            description: "Gouvernorat moderne au nord de Tunis",
            activeAuctions: "6 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes d'Ariana",
            population: "576,088 habitants",
            chef_lieu: "Ariana"
        },
        {
            name: "Ben Arous",
            region: "nord",
            description: "Gouvernorat industriel au sud de Tunis",
            activeAuctions: "8 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Ben Arous",
            population: "631,842 habitants",
            chef_lieu: "Ben Arous"
        },
        {
            name: "Manouba",
            region: "nord",
            description: "Gouvernorat agricole à l'ouest de Tunis",
            activeAuctions: "4 enchères",
            upcomingAuctions: "6 prochaines",
            customsOffice: "Bureau des douanes de Manouba",
            population: "379,518 habitants",
            chef_lieu: "Manouba"
        },
        {
            name: "Bizerte",
            region: "nord",
            description: "Port stratégique au nord de la Tunisie",
            activeAuctions: "7 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes de Bizerte",
            population: "568,219 habitants",
            chef_lieu: "Bizerte"
        },
        {
            name: "Nabeul",
            region: "nord",
            description: "Gouvernorat touristique du Cap Bon",
            activeAuctions: "9 enchères",
            upcomingAuctions: "5 prochaines",
            customsOffice: "Bureau des douanes de Nabeul",
            population: "787,920 habitants",
            chef_lieu: "Nabeul"
        },
        {
            name: "Zaghouan",
            region: "centre",
            description: "Gouvernorat montagneux et agricole",
            activeAuctions: "3 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Zaghouan",
            population: "176,945 habitants",
            chef_lieu: "Zaghouan"
        },
        {
            name: "Siliana",
            region: "centre",
            description: "Gouvernorat agricole du nord-ouest",
            activeAuctions: "2 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Siliana",
            population: "223,087 habitants",
            chef_lieu: "Siliana"
        },
        {
            name: "Le Kef",
            region: "centre",
            description: "Gouvernorat frontalier avec l'Algérie",
            activeAuctions: "4 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes du Kef",
            population: "243,156 habitants",
            chef_lieu: "Le Kef"
        },
        {
            name: "Jendouba",
            region: "centre",
            description: "Gouvernorat forestier du nord-ouest",
            activeAuctions: "5 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Jendouba",
            population: "401,477 habitants",
            chef_lieu: "Jendouba"
        },
        {
            name: "Béja",
            region: "centre",
            description: "Gouvernorat agricole et céréalier",
            activeAuctions: "3 enchères",
            upcomingAuctions: "5 prochaines",
            customsOffice: "Bureau des douanes de Béja",
            population: "303,032 habitants",
            chef_lieu: "Béja"
        },
        {
            name: "Kairouan",
            region: "centre",
            description: "Ville sainte et patrimoine mondial UNESCO",
            activeAuctions: "6 enchères",
            upcomingAuctions: "6 prochaines",
            customsOffice: "Bureau des douanes de Kairouan",
            population: "570,559 habitants",
            chef_lieu: "Kairouan"
        },
        {
            name: "Kasserine",
            region: "centre",
            description: "Gouvernorat montagneux du centre-ouest",
            activeAuctions: "4 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Kasserine",
            population: "439,243 habitants",
            chef_lieu: "Kasserine"
        },
        {
            name: "Sidi Bouzid",
            region: "centre",
            description: "Gouvernorat agricole du centre",
            activeAuctions: "3 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Sidi Bouzid",
            population: "429,912 habitants",
            chef_lieu: "Sidi Bouzid"
        },
        {
            name: "Sousse",
            region: "centre",
            description: "Ville côtière et centre touristique",
            activeAuctions: "12 enchères",
            upcomingAuctions: "7 prochaines",
            customsOffice: "Bureau des douanes de Sousse",
            population: "674,971 habitants",
            chef_lieu: "Sousse"
        },
        {
            name: "Monastir",
            region: "centre",
            description: "Ville côtière et aéroport international",
            activeAuctions: "9 enchères",
            upcomingAuctions: "5 prochaines",
            customsOffice: "Bureau des douanes de Monastir",
            population: "548,828 habitants",
            chef_lieu: "Monastir"
        },
        {
            name: "Mahdia",
            region: "centre",
            description: "Port de pêche et ville historique",
            activeAuctions: "5 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Mahdia",
            population: "410,812 habitants",
            chef_lieu: "Mahdia"
        },
        {
            name: "Sfax",
            region: "centre",
            description: "Deuxième ville de Tunisie et port important",
            activeAuctions: "18 enchères",
            upcomingAuctions: "9 prochaines",
            customsOffice: "Bureau des douanes de Sfax",
            population: "955,421 habitants",
            chef_lieu: "Sfax"
        },
        {
            name: "Gafsa",
            region: "sud",
            description: "Centre minier et porte du désert",
            activeAuctions: "7 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Gafsa",
            population: "337,331 habitants",
            chef_lieu: "Gafsa"
        },
        {
            name: "Tozeur",
            region: "sud",
            description: "Oasis du Jérid et tourisme saharien",
            activeAuctions: "3 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes de Tozeur",
            population: "107,912 habitants",
            chef_lieu: "Tozeur"
        },
        {
            name: "Kebili",
            region: "sud",
            description: "Gouvernorat saharien et oasis",
            activeAuctions: "2 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Kebili",
            population: "156,961 habitants",
            chef_lieu: "Kebili"
        },
        {
            name: "Gabès",
            region: "sud",
            description: "Oasis urbaine et centre industriel",
            activeAuctions: "8 enchères",
            upcomingAuctions: "6 prochaines",
            customsOffice: "Bureau des douanes de Gabès",
            population: "374,300 habitants",
            chef_lieu: "Gabès"
        },
        {
            name: "Médenine",
            region: "sud",
            description: "Porte du désert et architecture traditionnelle",
            activeAuctions: "5 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Médenine",
            population: "479,520 habitants",
            chef_lieu: "Médenine"
        },
        {
            name: "Tataouine",
            region: "sud",
            description: "Gouvernorat saharien du sud-est",
            activeAuctions: "2 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes de Tataouine",
            population: "149,453 habitants",
            chef_lieu: "Tataouine"
        }
    ];

    var governorateLayer = null;
    var currentFilter = 'all';
    var selectedGovernorat = null;

    // Fonction pour obtenir la couleur selon la région
    function getRegionColor(region) {
        switch(region) {
            case 'nord': return '#3498db';    // Bleu
            case 'centre': return '#f69314';  // Orange
            case 'sud': return '#e74c3c';     // Rouge
            default: return '#95a5a6';        // Gris
        }
    }

    // Fonction pour créer les polygones des gouvernorats
    function createGovernoratePolygons() {
        // Supprimer la couche existante
        if (governorateLayer) {
            map.removeLayer(governorateLayer);
        }

        // Filtrer les gouvernorats selon le filtre actuel
        var filteredFeatures = tunisiaGeoJSON.features.filter(function(feature) {
            return currentFilter === 'all' || feature.properties.region === currentFilter;
        });

        var filteredGeoJSON = {
            type: "FeatureCollection",
            features: filteredFeatures
        };

        // Créer la couche des gouvernorats
        governorateLayer = L.geoJSON(filteredGeoJSON, {
            style: function(feature) {
                var region = feature.properties.region;
                var isSelected = selectedGovernorat === feature.properties.name;

                return {
                    fillColor: getRegionColor(region),
                    weight: isSelected ? 4 : 2,
                    opacity: 1,
                    color: isSelected ? '#2c3e50' : '#34495e',
                    dashArray: '',
                    fillOpacity: isSelected ? 0.9 : 0.7
                };
            },
            onEachFeature: function(feature, layer) {
                var governoratName = feature.properties.name;
                var governoratData = gouvernorats.find(g => g.name === governoratName);

                if (governoratData) {
                    // Contenu du popup
                    var popupContent = `
                        <div style="text-align: center; min-width: 220px;">
                            <h5 style="color: #f69314; margin-bottom: 10px; font-weight: 600;">${governoratData.name}</h5>
                            <p style="margin-bottom: 8px; color: #6c757d; font-size: 0.9rem;">${governoratData.description}</p>
                            <p style="margin-bottom: 15px; color: #495057; font-size: 0.85rem;"><strong>${governoratData.population}</strong></p>
                            <button class="btn btn-primary btn-sm" onclick="selectGovernorat('${governoratData.name}')"
                                    style="border-radius: 20px; padding: 6px 15px; font-weight: 500;">
                                Sélectionner ce gouvernorat
                            </button>
                        </div>
                    `;

                    layer.bindPopup(popupContent, {
                        maxWidth: 280,
                        className: 'custom-popup'
                    });

                    // Événements de la souris
                    layer.on({
                        mouseover: function(e) {
                            if (selectedGovernorat !== governoratName) {
                                layer.setStyle({
                                    weight: 3,
                                    fillOpacity: 0.8,
                                    color: '#2c3e50'
                                });
                            }
                        },
                        mouseout: function(e) {
                            if (selectedGovernorat !== governoratName) {
                                layer.setStyle({
                                    weight: 2,
                                    fillOpacity: 0.7,
                                    color: '#34495e'
                                });
                            }
                        },
                        click: function(e) {
                            selectGovernorat(governoratName);
                        }
                    });
                }
            }
        }).addTo(map);
    }

    // Fonction pour sélectionner un gouvernorat
    window.selectGovernorat = function(governoratName) {
        var governorat = gouvernorats.find(g => g.name === governoratName);
        if (governorat) {
            selectedGovernorat = governoratName;

            // Mettre à jour l'interface utilisateur
            updateGovernorateInfo(governorat);

            // Recréer les polygones pour mettre en évidence la sélection
            createGovernoratePolygons();

            // Centrer la carte sur le gouvernorat sélectionné
            var feature = tunisiaGeoJSON.features.find(f => f.properties.name === governoratName);
            if (feature) {
                var bounds = L.geoJSON(feature).getBounds();
                map.fitBounds(bounds, {
                    padding: [20, 20],
                    animate: true,
                    duration: 1
                });
            }

            // Afficher le panneau d'informations avec animation
            showGovernorateInfoPanel();
        }
    };

    // Fonction pour mettre à jour les informations du gouvernorat
    function updateGovernorateInfo(governorat) {
        document.getElementById('selected-city-name').textContent = governorat.name;
        document.getElementById('selected-city-description').textContent = governorat.description;
        document.getElementById('active-auctions').textContent = governorat.activeAuctions;
        document.getElementById('upcoming-auctions').textContent = governorat.upcomingAuctions;
        document.getElementById('customs-office').textContent = governorat.customsOffice;
    }

    // Fonction pour afficher le panneau d'informations
    function showGovernorateInfoPanel() {
        var cityInfoPanel = document.getElementById('city-info');
        cityInfoPanel.style.display = 'block';

        // Scroll vers le panneau d'informations
        setTimeout(function() {
            cityInfoPanel.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 500);
    }

    // Fonction pour voir les enchères d'un gouvernorat
    window.viewCityAuctions = function() {
        var governoratName = document.getElementById('selected-city-name').textContent;
        if (governoratName) {
            // Ici vous pouvez ajouter la logique de redirection vers la page des enchères
            // Par exemple : window.location.href = `/encheres/${governoratName}`;
            alert(`Redirection vers les enchères du gouvernorat de ${governoratName}...\n\nCette fonctionnalité sera bientôt disponible.`);
        }
    };

    // Gestion des filtres par région
    function initializeFilters() {
        document.querySelectorAll('.filter-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                // Retirer la classe active de tous les boutons
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                
                // Ajouter la classe active au bouton cliqué
                this.classList.add('active');
                
                // Mettre à jour le filtre actuel
                currentFilter = this.getAttribute('data-region');
                
                // Recréer les polygones avec le nouveau filtre
                createGovernoratePolygons();

                // Masquer le panneau d'informations si un gouvernorat était sélectionné
                if (selectedGovernorat) {
                    document.getElementById('city-info').style.display = 'none';
                    selectedGovernorat = null;
                }
                
                // Ajuster la vue de la carte selon le filtre
                adjustMapView();
            });
        });
    }

    // Fonction pour ajuster la vue de la carte selon le filtre
    function adjustMapView() {
        var bounds;
        
        switch(currentFilter) {
            case 'nord':
                bounds = L.latLngBounds([[36.5, 8.5], [37.5, 11.5]]);
                break;
            case 'centre':
                bounds = L.latLngBounds([[34.5, 9.5], [36.5, 11.5]]);
                break;
            case 'sud':
                bounds = L.latLngBounds([[32.5, 8.0], [34.5, 11.0]]);
                break;
            default:
                bounds = L.latLngBounds([[32.0, 7.5], [38.0, 12.0]]);
        }
        
        map.fitBounds(bounds, {
            padding: [20, 20],
            animate: true,
            duration: 1
        });
    }

    // Initialisation
    function initialize() {
        try {
            // Vérifier si les données GeoJSON sont disponibles
            if (typeof tunisiaGeoJSON === 'undefined') {
                console.error('Les données GeoJSON des gouvernorats ne sont pas chargées');
                return;
            }

            createGovernoratePolygons();
            initializeFilters();

            // Ajuster la vue initiale
            adjustMapView();

            console.log('Carte des gouvernorats tunisiens initialisée avec succès');
        } catch (error) {
            console.error('Erreur lors de l\'initialisation de la carte:', error);
        }
    }

    // Lancer l'initialisation
    initialize();
});
