/**
 * Script pour la carte interactive de Tunisie
 * Gestion des marqueurs, filtres et interactions utilisateur
 */

document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si Leaflet est chargé
    if (typeof L === 'undefined') {
        console.error('Leaflet n\'est pas chargé. Veuillez vérifier que la bibliothèque Leaflet est incluse.');
        return;
    }

    // Initialisation de la carte
    var map = L.map('tunisia-map', {
        center: [34.0, 9.0],
        zoom: 7,
        zoomControl: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        dragging: true,
        touchZoom: true
    });

    // Ajout de la couche de tuiles avec attribution
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 18,
        minZoom: 6
    }).addTo(map);

    // Données des villes tunisiennes avec leurs informations détaillées
    var cities = [
        {
            name: "Tunis",
            lat: 36.8065,
            lng: 10.1815,
            region: "nord",
            description: "Capitale de la Tunisie et centre économique principal",
            activeAuctions: "15 enchères",
            upcomingAuctions: "8 prochaines",
            customsOffice: "Bureau principal des douanes",
            population: "1,056,247 habitants",
            gouvernorat: "Tunis"
        },
        {
            name: "Sfax",
            lat: 34.7406,
            lng: 10.7603,
            region: "centre",
            description: "Deuxième ville de Tunisie et port important",
            activeAuctions: "12 enchères",
            upcomingAuctions: "5 prochaines",
            customsOffice: "Bureau des douanes de Sfax",
            population: "330,440 habitants",
            gouvernorat: "Sfax"
        },
        {
            name: "Sousse",
            lat: 35.8256,
            lng: 10.6369,
            region: "centre",
            description: "Ville côtière et centre touristique",
            activeAuctions: "8 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Sousse",
            population: "271,428 habitants",
            gouvernorat: "Sousse"
        },
        {
            name: "Gabès",
            lat: 33.8815,
            lng: 10.0982,
            region: "sud",
            description: "Oasis urbaine et centre industriel",
            activeAuctions: "6 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Gabès",
            population: "152,921 habitants",
            gouvernorat: "Gabès"
        },
        {
            name: "Bizerte",
            lat: 37.2744,
            lng: 9.8739,
            region: "nord",
            description: "Port stratégique au nord de la Tunisie",
            activeAuctions: "7 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes de Bizerte",
            population: "142,966 habitants",
            gouvernorat: "Bizerte"
        },
        {
            name: "Kairouan",
            lat: 35.6781,
            lng: 10.0963,
            region: "centre",
            description: "Ville sainte et patrimoine mondial UNESCO",
            activeAuctions: "4 enchères",
            upcomingAuctions: "6 prochaines",
            customsOffice: "Bureau des douanes de Kairouan",
            population: "186,653 habitants",
            gouvernorat: "Kairouan"
        },
        {
            name: "Gafsa",
            lat: 34.425,
            lng: 8.7842,
            region: "sud",
            description: "Centre minier et porte du désert",
            activeAuctions: "5 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Gafsa",
            population: "111,170 habitants",
            gouvernorat: "Gafsa"
        },
        {
            name: "Monastir",
            lat: 35.7643,
            lng: 10.8113,
            region: "centre",
            description: "Ville côtière et aéroport international",
            activeAuctions: "9 enchères",
            upcomingAuctions: "7 prochaines",
            customsOffice: "Bureau des douanes de Monastir",
            population: "104,535 habitants",
            gouvernorat: "Monastir"
        },
        {
            name: "Ariana",
            lat: 36.8625,
            lng: 10.1647,
            region: "nord",
            description: "Ville moderne proche de la capitale",
            activeAuctions: "6 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes d'Ariana",
            population: "114,486 habitants",
            gouvernorat: "Ariana"
        },
        {
            name: "Médenine",
            lat: 33.3549,
            lng: 10.5055,
            region: "sud",
            description: "Porte du désert et architecture traditionnelle",
            activeAuctions: "3 enchères",
            upcomingAuctions: "5 prochaines",
            customsOffice: "Bureau des douanes de Médenine",
            population: "61,705 habitants",
            gouvernorat: "Médenine"
        }
    ];

    var markers = [];
    var currentFilter = 'all';
    var selectedCity = null;

    // Fonction pour créer les marqueurs sur la carte
    function createMarkers() {
        // Supprimer les marqueurs existants
        markers.forEach(marker => map.removeLayer(marker));
        markers = [];

        cities.forEach(function(city) {
            if (currentFilter === 'all' || city.region === currentFilter) {
                // Créer un marqueur circulaire personnalisé
                var marker = L.circleMarker([city.lat, city.lng], {
                    radius: 12,
                    fillColor: '#f69314',
                    color: 'white',
                    weight: 3,
                    opacity: 1,
                    fillOpacity: 0.8,
                    className: 'custom-marker'
                }).addTo(map);

                // Contenu du popup
                var popupContent = `
                    <div style="text-align: center; min-width: 200px;">
                        <h5 style="color: #f69314; margin-bottom: 10px; font-weight: 600;">${city.name}</h5>
                        <p style="margin-bottom: 8px; color: #6c757d; font-size: 0.9rem;">${city.description}</p>
                        <p style="margin-bottom: 15px; color: #495057; font-size: 0.85rem;"><strong>${city.population}</strong></p>
                        <button class="btn btn-primary btn-sm" onclick="selectCity('${city.name}')" 
                                style="border-radius: 20px; padding: 6px 15px; font-weight: 500;">
                            Sélectionner cette ville
                        </button>
                    </div>
                `;

                marker.bindPopup(popupContent, {
                    maxWidth: 250,
                    className: 'custom-popup'
                });

                // Événement de clic sur le marqueur
                marker.on('click', function() {
                    selectCity(city.name);
                });

                // Effet de survol
                marker.on('mouseover', function() {
                    this.setStyle({
                        radius: 15,
                        fillOpacity: 1
                    });
                });

                marker.on('mouseout', function() {
                    if (selectedCity !== city.name) {
                        this.setStyle({
                            radius: 12,
                            fillOpacity: 0.8
                        });
                    }
                });

                markers.push(marker);
            }
        });
    }

    // Fonction pour sélectionner une ville
    window.selectCity = function(cityName) {
        var city = cities.find(c => c.name === cityName);
        if (city) {
            selectedCity = cityName;
            
            // Mettre à jour l'interface utilisateur
            updateCityInfo(city);
            
            // Centrer la carte sur la ville sélectionnée avec animation
            map.setView([city.lat, city.lng], 10, {
                animate: true,
                duration: 1
            });

            // Mettre en évidence le marqueur sélectionné
            highlightSelectedMarker(city);
            
            // Afficher le panneau d'informations avec animation
            showCityInfoPanel();
        }
    };

    // Fonction pour mettre à jour les informations de la ville
    function updateCityInfo(city) {
        document.getElementById('selected-city-name').textContent = city.name;
        document.getElementById('selected-city-description').textContent = city.description;
        document.getElementById('active-auctions').textContent = city.activeAuctions;
        document.getElementById('upcoming-auctions').textContent = city.upcomingAuctions;
        document.getElementById('customs-office').textContent = city.customsOffice;
    }

    // Fonction pour afficher le panneau d'informations
    function showCityInfoPanel() {
        var cityInfoPanel = document.getElementById('city-info');
        cityInfoPanel.style.display = 'block';
        
        // Scroll vers le panneau d'informations
        setTimeout(function() {
            cityInfoPanel.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'nearest' 
            });
        }, 500);
    }

    // Fonction pour mettre en évidence le marqueur sélectionné
    function highlightSelectedMarker(selectedCity) {
        markers.forEach(function(marker) {
            var markerLatLng = marker.getLatLng();
            if (markerLatLng.lat === selectedCity.lat && markerLatLng.lng === selectedCity.lng) {
                marker.setStyle({
                    radius: 15,
                    fillOpacity: 1,
                    weight: 4
                });
            } else {
                marker.setStyle({
                    radius: 12,
                    fillOpacity: 0.8,
                    weight: 3
                });
            }
        });
    }

    // Fonction pour voir les enchères d'une ville
    window.viewCityAuctions = function() {
        var cityName = document.getElementById('selected-city-name').textContent;
        if (cityName) {
            // Ici vous pouvez ajouter la logique de redirection vers la page des enchères
            // Par exemple : window.location.href = `/encheres/${cityName}`;
            alert(`Redirection vers les enchères de ${cityName}...\n\nCette fonctionnalité sera bientôt disponible.`);
        }
    };

    // Gestion des filtres par région
    function initializeFilters() {
        document.querySelectorAll('.filter-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                // Retirer la classe active de tous les boutons
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                
                // Ajouter la classe active au bouton cliqué
                this.classList.add('active');
                
                // Mettre à jour le filtre actuel
                currentFilter = this.getAttribute('data-region');
                
                // Recréer les marqueurs avec le nouveau filtre
                createMarkers();
                
                // Masquer le panneau d'informations si une ville était sélectionnée
                if (selectedCity) {
                    document.getElementById('city-info').style.display = 'none';
                    selectedCity = null;
                }
                
                // Ajuster la vue de la carte selon le filtre
                adjustMapView();
            });
        });
    }

    // Fonction pour ajuster la vue de la carte selon le filtre
    function adjustMapView() {
        var bounds;
        
        switch(currentFilter) {
            case 'nord':
                bounds = L.latLngBounds([[36.5, 8.5], [37.5, 11.5]]);
                break;
            case 'centre':
                bounds = L.latLngBounds([[34.5, 9.5], [36.5, 11.5]]);
                break;
            case 'sud':
                bounds = L.latLngBounds([[32.5, 8.0], [34.5, 11.0]]);
                break;
            default:
                bounds = L.latLngBounds([[32.0, 7.5], [38.0, 12.0]]);
        }
        
        map.fitBounds(bounds, {
            padding: [20, 20],
            animate: true,
            duration: 1
        });
    }

    // Initialisation
    function initialize() {
        try {
            createMarkers();
            initializeFilters();
            
            // Ajuster la vue initiale
            adjustMapView();
            
            console.log('Carte de Tunisie initialisée avec succès');
        } catch (error) {
            console.error('Erreur lors de l\'initialisation de la carte:', error);
        }
    }

    // Lancer l'initialisation
    initialize();
});
