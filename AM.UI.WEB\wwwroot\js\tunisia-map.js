/**
 * Script pour la carte interactive de Tunisie
 * Gestion des marqueurs, filtres et interactions utilisateur
 */

document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si Leaflet est chargé
    if (typeof L === 'undefined') {
        console.error('Leaflet n\'est pas chargé. Veuillez vérifier que la bibliothèque Leaflet est incluse.');
        return;
    }

    // Initialisation de la carte
    var map = L.map('tunisia-map', {
        center: [34.0, 9.0],
        zoom: 7,
        zoomControl: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        dragging: true,
        touchZoom: true
    });

    // Pas de couche de tuiles - fond personnalisé uniquement
    // Le fond sera géré par CSS pour un look plus moderne

    // Données des gouvernorats tunisiens avec leurs informations
    var gouvernorats = [
        {
            name: "Tunis",
            region: "nord",
            description: "Capitale de la Tunisie et centre économique principal",
            activeAuctions: "15 enchères",
            upcomingAuctions: "8 prochaines",
            customsOffice: "Bureau principal des douanes",
            population: "1,056,247 habitants",
            chef_lieu: "Tunis"
        },
        {
            name: "Ariana",
            region: "nord",
            description: "Gouvernorat moderne au nord de Tunis",
            activeAuctions: "6 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes d'Ariana",
            population: "576,088 habitants",
            chef_lieu: "Ariana"
        },
        {
            name: "Ben Arous",
            region: "nord",
            description: "Gouvernorat industriel au sud de Tunis",
            activeAuctions: "8 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Ben Arous",
            population: "631,842 habitants",
            chef_lieu: "Ben Arous"
        },
        {
            name: "Manouba",
            region: "nord",
            description: "Gouvernorat agricole à l'ouest de Tunis",
            activeAuctions: "4 enchères",
            upcomingAuctions: "6 prochaines",
            customsOffice: "Bureau des douanes de Manouba",
            population: "379,518 habitants",
            chef_lieu: "Manouba"
        },
        {
            name: "Bizerte",
            region: "nord",
            description: "Port stratégique au nord de la Tunisie",
            activeAuctions: "7 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes de Bizerte",
            population: "568,219 habitants",
            chef_lieu: "Bizerte"
        },
        {
            name: "Nabeul",
            region: "nord",
            description: "Gouvernorat touristique du Cap Bon",
            activeAuctions: "9 enchères",
            upcomingAuctions: "5 prochaines",
            customsOffice: "Bureau des douanes de Nabeul",
            population: "787,920 habitants",
            chef_lieu: "Nabeul"
        },
        {
            name: "Zaghouan",
            region: "centre",
            description: "Gouvernorat montagneux et agricole",
            activeAuctions: "3 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Zaghouan",
            population: "176,945 habitants",
            chef_lieu: "Zaghouan"
        },
        {
            name: "Siliana",
            region: "centre",
            description: "Gouvernorat agricole du nord-ouest",
            activeAuctions: "2 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Siliana",
            population: "223,087 habitants",
            chef_lieu: "Siliana"
        },
        {
            name: "Le Kef",
            region: "centre",
            description: "Gouvernorat frontalier avec l'Algérie",
            activeAuctions: "4 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes du Kef",
            population: "243,156 habitants",
            chef_lieu: "Le Kef"
        },
        {
            name: "Jendouba",
            region: "centre",
            description: "Gouvernorat forestier du nord-ouest",
            activeAuctions: "5 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Jendouba",
            population: "401,477 habitants",
            chef_lieu: "Jendouba"
        },
        {
            name: "Béja",
            region: "centre",
            description: "Gouvernorat agricole et céréalier",
            activeAuctions: "3 enchères",
            upcomingAuctions: "5 prochaines",
            customsOffice: "Bureau des douanes de Béja",
            population: "303,032 habitants",
            chef_lieu: "Béja"
        },
        {
            name: "Kairouan",
            region: "centre",
            description: "Ville sainte et patrimoine mondial UNESCO",
            activeAuctions: "6 enchères",
            upcomingAuctions: "6 prochaines",
            customsOffice: "Bureau des douanes de Kairouan",
            population: "570,559 habitants",
            chef_lieu: "Kairouan"
        },
        {
            name: "Kasserine",
            region: "centre",
            description: "Gouvernorat montagneux du centre-ouest",
            activeAuctions: "4 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Kasserine",
            population: "439,243 habitants",
            chef_lieu: "Kasserine"
        },
        {
            name: "Sidi Bouzid",
            region: "centre",
            description: "Gouvernorat agricole du centre",
            activeAuctions: "3 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Sidi Bouzid",
            population: "429,912 habitants",
            chef_lieu: "Sidi Bouzid"
        },
        {
            name: "Sousse",
            region: "centre",
            description: "Ville côtière et centre touristique",
            activeAuctions: "12 enchères",
            upcomingAuctions: "7 prochaines",
            customsOffice: "Bureau des douanes de Sousse",
            population: "674,971 habitants",
            chef_lieu: "Sousse"
        },
        {
            name: "Monastir",
            region: "centre",
            description: "Ville côtière et aéroport international",
            activeAuctions: "9 enchères",
            upcomingAuctions: "5 prochaines",
            customsOffice: "Bureau des douanes de Monastir",
            population: "548,828 habitants",
            chef_lieu: "Monastir"
        },
        {
            name: "Mahdia",
            region: "centre",
            description: "Port de pêche et ville historique",
            activeAuctions: "5 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Mahdia",
            population: "410,812 habitants",
            chef_lieu: "Mahdia"
        },
        {
            name: "Sfax",
            region: "centre",
            description: "Deuxième ville de Tunisie et port important",
            activeAuctions: "18 enchères",
            upcomingAuctions: "9 prochaines",
            customsOffice: "Bureau des douanes de Sfax",
            population: "955,421 habitants",
            chef_lieu: "Sfax"
        },
        {
            name: "Gafsa",
            region: "sud",
            description: "Centre minier et porte du désert",
            activeAuctions: "7 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Gafsa",
            population: "337,331 habitants",
            chef_lieu: "Gafsa"
        },
        {
            name: "Tozeur",
            region: "sud",
            description: "Oasis du Jérid et tourisme saharien",
            activeAuctions: "3 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes de Tozeur",
            population: "107,912 habitants",
            chef_lieu: "Tozeur"
        },
        {
            name: "Kebili",
            region: "sud",
            description: "Gouvernorat saharien et oasis",
            activeAuctions: "2 enchères",
            upcomingAuctions: "3 prochaines",
            customsOffice: "Bureau des douanes de Kebili",
            population: "156,961 habitants",
            chef_lieu: "Kebili"
        },
        {
            name: "Gabès",
            region: "sud",
            description: "Oasis urbaine et centre industriel",
            activeAuctions: "8 enchères",
            upcomingAuctions: "6 prochaines",
            customsOffice: "Bureau des douanes de Gabès",
            population: "374,300 habitants",
            chef_lieu: "Gabès"
        },
        {
            name: "Médenine",
            region: "sud",
            description: "Porte du désert et architecture traditionnelle",
            activeAuctions: "5 enchères",
            upcomingAuctions: "4 prochaines",
            customsOffice: "Bureau des douanes de Médenine",
            population: "479,520 habitants",
            chef_lieu: "Médenine"
        },
        {
            name: "Tataouine",
            region: "sud",
            description: "Gouvernorat saharien du sud-est",
            activeAuctions: "2 enchères",
            upcomingAuctions: "2 prochaines",
            customsOffice: "Bureau des douanes de Tataouine",
            population: "149,453 habitants",
            chef_lieu: "Tataouine"
        }
    ];

    var governorateLayer = null;
    var currentFilter = 'all';
    var selectedGovernorat = null;

    // Fonction pour obtenir la couleur selon la région (style moderne)
    function getRegionColor(region, isSelected = false, isHovered = false) {
        const colors = {
            'nord': {
                normal: '#ffffff',      // Blanc élégant
                selected: '#667eea',    // Bleu moderne
                hovered: '#f8f9fa'      // Blanc légèrement teinté
            },
            'centre': {
                normal: '#e8f4f8',      // Bleu très clair
                selected: '#4facfe',    // Bleu ciel
                hovered: '#d1ecf1'      // Bleu clair
            },
            'sud': {
                normal: '#f0f8ff',      // Bleu alice très clair
                selected: '#00d2ff',    // Cyan moderne
                hovered: '#e6f3ff'      // Bleu très pâle
            }
        };

        const regionColors = colors[region] || colors['centre'];

        if (isSelected) return regionColors.selected;
        if (isHovered) return regionColors.hovered;
        return regionColors.normal;
    }

    // Fonction pour obtenir la couleur de bordure
    function getBorderColor(region, isSelected = false) {
        if (isSelected) return '#ffffff';

        switch(region) {
            case 'nord': return '#b8c6db';
            case 'centre': return '#a8d8ea';
            case 'sud': return '#91c5f7';
            default: return '#b8c6db';
        }
    }

    // Fonction pour créer les polygones des gouvernorats
    function createGovernoratePolygons() {
        // Supprimer la couche existante
        if (governorateLayer) {
            map.removeLayer(governorateLayer);
        }

        // Filtrer les gouvernorats selon le filtre actuel
        var filteredFeatures = tunisiaGeoJSON.features.filter(function(feature) {
            return currentFilter === 'all' || feature.properties.region === currentFilter;
        });

        var filteredGeoJSON = {
            type: "FeatureCollection",
            features: filteredFeatures
        };

        // Créer la couche des gouvernorats
        governorateLayer = L.geoJSON(filteredGeoJSON, {
            style: function(feature) {
                var region = feature.properties.region;
                var isSelected = selectedGovernorat === feature.properties.name;

                return {
                    fillColor: getRegionColor(region, isSelected),
                    weight: isSelected ? 3 : 1.5,
                    opacity: 1,
                    color: getBorderColor(region, isSelected),
                    dashArray: '',
                    fillOpacity: 0.9,
                    className: isSelected ? 'governorate-selected' : ''
                };
            },
            onEachFeature: function(feature, layer) {
                var governoratName = feature.properties.name;
                var governoratData = gouvernorats.find(g => g.name === governoratName);

                if (governoratData) {
                    // Contenu du popup moderne
                    var popupContent = `
                        <div style="text-align: center; min-width: 250px;">
                            <h5>${governoratData.name}</h5>
                            <p style="margin-bottom: 8px;">${governoratData.description}</p>
                            <p style="margin-bottom: 15px;"><strong>${governoratData.population}</strong></p>
                            <div style="margin-bottom: 12px;">
                                <span style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 15px; font-size: 0.8rem; margin: 2px;">
                                    ${governoratData.activeAuctions}
                                </span>
                                <span style="display: inline-block; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 4px 12px; border-radius: 15px; font-size: 0.8rem; margin: 2px;">
                                    ${governoratData.upcomingAuctions}
                                </span>
                            </div>
                            <button class="btn btn-primary btn-sm" onclick="selectGovernorat('${governoratData.name}')">
                                Sélectionner
                            </button>
                        </div>
                    `;

                    layer.bindPopup(popupContent, {
                        maxWidth: 280,
                        className: 'custom-popup'
                    });

                    // Événements de la souris avec animations fluides
                    layer.on({
                        mouseover: function(e) {
                            if (selectedGovernorat !== governoratName) {
                                var region = feature.properties.region;
                                layer.setStyle({
                                    weight: 2.5,
                                    fillColor: getRegionColor(region, false, true),
                                    color: getBorderColor(region),
                                    fillOpacity: 1
                                });

                                // Effet de brillance
                                layer.getElement().style.filter = 'brightness(1.1) drop-shadow(0 0 10px rgba(255,255,255,0.5))';
                            }
                        },
                        mouseout: function(e) {
                            if (selectedGovernorat !== governoratName) {
                                var region = feature.properties.region;
                                layer.setStyle({
                                    weight: 1.5,
                                    fillColor: getRegionColor(region),
                                    color: getBorderColor(region),
                                    fillOpacity: 0.9
                                });

                                // Retirer l'effet de brillance
                                layer.getElement().style.filter = 'none';
                            }
                        },
                        click: function(e) {
                            selectGovernorat(governoratName);

                            // Effet de clic avec animation
                            layer.getElement().style.transform = 'scale(0.95)';
                            setTimeout(() => {
                                layer.getElement().style.transform = 'scale(1)';
                            }, 150);
                        }
                    });
                }
            }
        }).addTo(map);
    }

    // Fonction pour sélectionner un gouvernorat
    window.selectGovernorat = function(governoratName) {
        var governorat = gouvernorats.find(g => g.name === governoratName);
        if (governorat) {
            selectedGovernorat = governoratName;

            // Mettre à jour l'interface utilisateur
            updateGovernorateInfo(governorat);

            // Recréer les polygones pour mettre en évidence la sélection
            createGovernoratePolygons();

            // Centrer la carte sur le gouvernorat sélectionné
            var feature = tunisiaGeoJSON.features.find(f => f.properties.name === governoratName);
            if (feature) {
                var bounds = L.geoJSON(feature).getBounds();
                map.fitBounds(bounds, {
                    padding: [20, 20],
                    animate: true,
                    duration: 1
                });
            }

            // Afficher le panneau d'informations avec animation
            showGovernorateInfoPanel();
        }
    };

    // Fonction pour mettre à jour les informations du gouvernorat
    function updateGovernorateInfo(governorat) {
        document.getElementById('selected-city-name').textContent = governorat.name;
        document.getElementById('selected-city-description').textContent = governorat.description;
        document.getElementById('active-auctions').textContent = governorat.activeAuctions;
        document.getElementById('upcoming-auctions').textContent = governorat.upcomingAuctions;
        document.getElementById('customs-office').textContent = governorat.customsOffice;
    }

    // Fonction pour afficher le panneau d'informations
    function showGovernorateInfoPanel() {
        var cityInfoPanel = document.getElementById('city-info');
        cityInfoPanel.style.display = 'block';

        // Scroll vers le panneau d'informations
        setTimeout(function() {
            cityInfoPanel.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 500);
    }

    // Fonction pour voir les enchères d'un gouvernorat
    window.viewCityAuctions = function() {
        var governoratName = document.getElementById('selected-city-name').textContent;
        if (governoratName) {
            // Ici vous pouvez ajouter la logique de redirection vers la page des enchères
            // Par exemple : window.location.href = `/encheres/${governoratName}`;
            alert(`Redirection vers les enchères du gouvernorat de ${governoratName}...\n\nCette fonctionnalité sera bientôt disponible.`);
        }
    };

    // Gestion des filtres par région
    function initializeFilters() {
        document.querySelectorAll('.filter-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                // Retirer la classe active de tous les boutons
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                
                // Ajouter la classe active au bouton cliqué
                this.classList.add('active');
                
                // Mettre à jour le filtre actuel
                currentFilter = this.getAttribute('data-region');
                
                // Recréer les polygones avec le nouveau filtre
                createGovernoratePolygons();

                // Masquer le panneau d'informations si un gouvernorat était sélectionné
                if (selectedGovernorat) {
                    document.getElementById('city-info').style.display = 'none';
                    selectedGovernorat = null;
                }
                
                // Ajuster la vue de la carte selon le filtre
                adjustMapView();
            });
        });
    }

    // Fonction pour ajuster la vue de la carte selon le filtre
    function adjustMapView() {
        var bounds;
        
        switch(currentFilter) {
            case 'nord':
                bounds = L.latLngBounds([[36.5, 8.5], [37.5, 11.5]]);
                break;
            case 'centre':
                bounds = L.latLngBounds([[34.5, 9.5], [36.5, 11.5]]);
                break;
            case 'sud':
                bounds = L.latLngBounds([[32.5, 8.0], [34.5, 11.0]]);
                break;
            default:
                bounds = L.latLngBounds([[32.0, 7.5], [38.0, 12.0]]);
        }
        
        map.fitBounds(bounds, {
            padding: [20, 20],
            animate: true,
            duration: 1
        });
    }

    // Fonction pour afficher un indicateur de chargement
    function showLoadingIndicator() {
        const mapContainer = document.getElementById('tunisia-map');
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'map-loading';
        loadingDiv.innerHTML = `
            <div style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                color: white;
                z-index: 1000;
            ">
                <div style="
                    width: 50px;
                    height: 50px;
                    border: 3px solid rgba(255,255,255,0.3);
                    border-top: 3px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 15px;
                "></div>
                <p style="margin: 0; font-weight: 600; font-size: 1.1rem;">Chargement de la carte...</p>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        mapContainer.appendChild(loadingDiv);
    }

    // Fonction pour masquer l'indicateur de chargement
    function hideLoadingIndicator() {
        const loadingDiv = document.getElementById('map-loading');
        if (loadingDiv) {
            loadingDiv.style.opacity = '0';
            setTimeout(() => {
                loadingDiv.remove();
            }, 300);
        }
    }

    // Initialisation avec effet de chargement
    function initialize() {
        try {
            // Afficher l'indicateur de chargement
            showLoadingIndicator();

            // Vérifier si les données GeoJSON sont disponibles
            if (typeof tunisiaGeoJSON === 'undefined') {
                console.error('Les données GeoJSON des gouvernorats ne sont pas chargées');
                hideLoadingIndicator();
                return;
            }

            // Délai pour l'effet visuel
            setTimeout(() => {
                createGovernoratePolygons();
                initializeFilters();

                // Ajuster la vue initiale
                adjustMapView();

                // Masquer l'indicateur de chargement
                hideLoadingIndicator();

                // Ajouter un effet d'apparition aux gouvernorats
                setTimeout(() => {
                    const governorates = document.querySelectorAll('.leaflet-interactive');
                    governorates.forEach((gov, index) => {
                        setTimeout(() => {
                            gov.style.opacity = '0';
                            gov.style.transform = 'scale(0.8)';
                            gov.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';

                            setTimeout(() => {
                                gov.style.opacity = '1';
                                gov.style.transform = 'scale(1)';
                            }, 50);
                        }, index * 50);
                    });
                }, 200);

                console.log('🗺️ Carte des gouvernorats tunisiens initialisée avec succès');
            }, 800);

        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation de la carte:', error);
            hideLoadingIndicator();
        }
    }

    // Lancer l'initialisation
    initialize();
});
