/* _content/AM.UI.WEB/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-vz5kwzpl2k] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-vz5kwzpl2k] {
  color: #0077cc;
}

.btn-primary[b-vz5kwzpl2k] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-vz5kwzpl2k], .nav-pills .show > .nav-link[b-vz5kwzpl2k] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-vz5kwzpl2k] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-vz5kwzpl2k] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-vz5kwzpl2k] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-vz5kwzpl2k] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-vz5kwzpl2k] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
