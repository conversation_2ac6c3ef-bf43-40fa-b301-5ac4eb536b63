﻿using AM.ApplicationCore.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;

namespace AM.Infrastructure
{
    public class UnitOfWork : IUnitOfWork, IDisposable
    {
        private readonly DbContext _context;
        private readonly Type _repositoryType;
        private bool disposedValue;

        // Constructor that accepts both the context and repository type
        public UnitOfWork(AMContext context, Type repositoryType)
        {
            _context = context;
            _repositoryType = repositoryType;
        }

        // Generic repository method to get a repository for the entity type T
        public IGenericRepository<T> Repository<T>() where T : class
        {
            return (IGenericRepository<T>)Activator.CreateInstance(_repositoryType
                        .MakeGenericType(typeof(T)), _context);
        }

        // Save changes to the database
        public int Save()
        {
            return _context.SaveChanges();
        }

        // Dispose method to clean up resources
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
                disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
    }
}
