<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<!-- CSS personnalisé pour la carte -->
<link rel="stylesheet" href="~/css/tunisia-map.css" />

<div class="py-5 bg-light site-section tunisia-map-section" id="tunisia-map-section">
    <div class="container">
        <div class="row mb-5 justify-content-center">
            <div class="col-md-8 text-center">
                <h2 class="section-title mb-3">Sélectionnez votre gouvernorat</h2>
                <p class="lead">Découvrez les enchères disponibles dans votre gouvernorat en cliquant directement sur la carte de la Tunisie.</p>
            </div>
        </div>
        
        <!-- Filtres par région -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="city-filters text-center">
                    <button class="btn btn-outline-primary filter-btn active" data-region="all">Toutes les régions</button>
                    <button class="btn btn-outline-primary filter-btn" data-region="nord">Nord</button>
                    <button class="btn btn-outline-primary filter-btn" data-region="centre">Centre</button>
                    <button class="btn btn-outline-primary filter-btn" data-region="sud">Sud</button>
                </div>
            </div>
        </div>

        <!-- Carte interactive -->
        <div class="row">
            <div class="col-md-9">
                <div id="tunisia-map" style="height: 500px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);"></div>
            </div>
            <div class="col-md-3">
                <div class="region-legend">
                    <h6 class="legend-title">Régions de Tunisie</h6>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);"></div>
                        <span class="legend-label">Nord</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #e8f4f8 0%, #d1ecf1 100%);"></div>
                        <span class="legend-label">Centre</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);"></div>
                        <span class="legend-label">Sud</span>
                    </div>
                    <hr style="border: none; height: 2px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); margin: 20px 0;">
                    <div style="text-align: center; padding: 15px; background: rgba(102, 126, 234, 0.1); border-radius: 10px; margin-top: 15px;">
                        <p style="margin: 0; color: #2c3e50; font-size: 0.9rem; font-weight: 500;">
                            <i class="fas fa-mouse-pointer" style="color: #667eea; margin-right: 8px;"></i>
                            Cliquez sur un gouvernorat pour découvrir les enchères disponibles
                        </p>
                    </div>
                    <div style="text-align: center; margin-top: 15px;">
                        <div style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px 15px; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">
                            24 Gouvernorats
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informations sur la ville sélectionnée -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div id="city-info" class="city-info-panel" style="display: none;">
                    <div class="card">
                        <div class="card-body text-center">
                            <h4 id="selected-city-name" class="text-primary"></h4>
                            <p id="selected-city-description"></p>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="info-item">
                                        <span class="custom-icon flaticon-house text-primary"></span>
                                        <h5>Enchères actives</h5>
                                        <p id="active-auctions">-</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="info-item">
                                        <span class="custom-icon flaticon-coin text-primary"></span>
                                        <h5>Prochaines enchères</h5>
                                        <p id="upcoming-auctions">-</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="info-item">
                                        <span class="custom-icon flaticon-home text-primary"></span>
                                        <h5>Bureau des douanes</h5>
                                        <p id="customs-office">-</p>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-primary mt-3" onclick="viewCityAuctions()">Voir les enchères du gouvernorat</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<!-- Données géographiques des gouvernorats -->
<script src="~/js/tunisia-governorates-geojson.js"></script>
<!-- JavaScript personnalisé pour la carte -->
<script src="~/js/tunisia-map.js"></script>


