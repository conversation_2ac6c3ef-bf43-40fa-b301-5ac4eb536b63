      <header class="site-navbar py-4 js-sticky-header site-navbar-target" role="banner">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-6 col-xl-2">
                        <h1 class="mb-0 site-logo m-0 p-0"><a href="index.html" class="mb-0">LegalSpot</a></h1>
                    </div>
                    <div class="col-12 col-md-10 d-none d-xl-block">
                        <nav class="site-navigation position-relative text-right" role="navigation">
                            <ul class="site-menu main-menu js-clone-nav mr-auto d-none d-lg-block">
                                <li><a href="#home-section" class="nav-link">Accueil</a></li>
                                <li><a href="#lots-section" class="nav-link">Lots</a></li>
                                <li><a href="#howitworks-section" class="nav-link">Comment ça marche</a></li>
                        <li class="nav-item position-relative">
                            <a href="#" class="nav-link" id="ville-toggle">Ville</a>
                            <div id="dropdown-villes" class="dropdown-ville-cols"></div>
                        </li>


                        <li><a href="#about-section" class="nav-link">À propos</a></li>
                                <li><a href="#news-section" class="nav-link">Actualités</a></li>
                                <li><a href="#contact-section" class="nav-link">Contact</a></li>
                            </ul>
                        </nav>
                    </div>
                    <div class="col-6 d-inline-block d-xl-none ml-md-0 py-3"><a href="#" class="site-menu-toggle js-menu-toggle text-black float-right"><span class="icon-menu h3"></span></a></div>
                </div>
            </div>
        </header>
<style>
    .dropdown-ville-cols {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background: #fff;
        padding: 15px;
        border: 1px solid #ccc;
        width: 100%;
        max-width: 700px;
        display: flex;
        justify-content: space-between;
        gap: 15px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        z-index: 1000;
        overflow: hidden;
    }

    .ville-col {
        flex: 1;
        display: flex;
        flex-direction: column;
        max-height: 300px;
        overflow: hidden;
        border: 1px solid #eee;
        border-radius: 5px;
    }

        .ville-col h6 {
            margin: 0;
            padding: 8px;
            font-weight: bold;
            text-align: center;
            color: #fff;
            background-color: #f57c00;
            position: sticky;
            top: 0;
            z-index: 2;
        }

        .ville-col.centre h6 {
            background-color: #fbc02d;
            color: #000;
        }

        .ville-col.sud h6 {
            background-color: #d32f2f;
        }

    .ville-list {
        overflow-y: auto;
        flex-grow: 1;
    }

        .ville-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

            .ville-list ul li {
                padding: 6px 10px;
                margin: 4px;
                background-color: #f5f5f5;
                border-radius: 4px;
                text-align: center;
                cursor: pointer;
                transition: background 0.2s;
            }

                .ville-list ul li:hover {
                    background-color: #e0e0e0;
                }
</style>
