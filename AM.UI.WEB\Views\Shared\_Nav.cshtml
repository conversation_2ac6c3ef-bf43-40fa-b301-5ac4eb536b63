      <header class="site-navbar py-4 js-sticky-header site-navbar-target" role="banner">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-6 col-xl-2">
                        <h1 class="mb-0 site-logo m-0 p-0"><a href="index.html" class="mb-0">LegalSpot</a></h1>
                    </div>
                    <div class="col-12 col-md-10 d-none d-xl-block">
                        <nav class="site-navigation position-relative text-right" role="navigation">
                            <ul class="site-menu main-menu js-clone-nav mr-auto d-none d-lg-block">
                                <li><a href="#home-section" class="nav-link">Accueil</a></li>
                                <li><a href="#lots-section" class="nav-link">Lots</a></li>
                                <li><a href="#howitworks-section" class="nav-link">Comment ça marche</a></li>
                        <li class="nav-item position-relative">
                            <a href="#" class="nav-link" id="ville-toggle">Ville</a>
                            <div id="dropdown-villes" class="dropdown-ville-grouped"></div>
                        </li>

                        <li><a href="#about-section" class="nav-link">À propos</a></li>
                                <li><a href="#news-section" class="nav-link">Actualités</a></li>
                                <li><a href="#contact-section" class="nav-link">Contact</a></li>
                            </ul>
                        </nav>
                    </div>
                    <div class="col-6 d-inline-block d-xl-none ml-md-0 py-3"><a href="#" class="site-menu-toggle js-menu-toggle text-black float-right"><span class="icon-menu h3"></span></a></div>
                </div>
            </div>
        </header>
<style>
    .dropdown-ville-grouped {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background: #fff;
        padding: 20px;
        border: 1px solid #ccc;
        width: 600px;
        z-index: 1000;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .ville-region {
        margin-bottom: 15px;
    }

        .ville-region h6 {
            margin: 0 0 10px;
            font-weight: bold;
            font-size: 16px;
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
        }

        .ville-region ul {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            list-style: none;
            padding-left: 0;
        }

            .ville-region ul li {
                background: #f0f0f0;
                padding: 6px 10px;
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.2s;
            }

                .ville-region ul li:hover {
                    background: #e0e0e0;
                }

        /* Couleurs par région */
        .ville-region.nord h6 {
            background-color: #f57c00;
        }

        .ville-region.centre h6 {
            background-color: #fbc02d;
        }

        .ville-region.sud h6 {
            background-color: #d32f2f;
        }
</style>
