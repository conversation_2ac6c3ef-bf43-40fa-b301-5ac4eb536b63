# Carte Interactive de Tunisie - Documentation

## Vue d'ensemble

Cette fonctionnalité remplace la section "Comment ça marche" par une carte interactive de la Tunisie permettant aux utilisateurs de sélectionner directement leur ville pour découvrir les enchères disponibles dans leur région.

## Fonctionnalités

### 1. Carte Interactive
- **Bibliothèque utilisée** : Leaflet.js (open source)
- **Centrage** : Tunisie (latitude: 34.0, longitude: 9.0)
- **Zoom** : Adaptatif selon les filtres de région
- **Marqueurs** : Villes principales avec informations détaillées

### 2. Filtres par Région
- **Toutes les régions** : Affiche toutes les villes
- **Nord** : Tunis, Bizerte, Ariana
- **Centre** : Sfax, Sousse, Kairouan, Monastir
- **Sud** : Gabès, Gafsa, Médenine

### 3. Villes Incluses
1. **Tunis** - Capitale et centre économique
2. **Sfax** - Port important et deuxième ville
3. **Sousse** - Centre touristique côtier
4. **Gabès** - Oasis urbaine industrielle
5. **Bizerte** - Port stratégique du nord
6. **Kairouan** - Ville sainte UNESCO
7. **Gafsa** - Centre minier
8. **Monastir** - Ville côtière avec aéroport
9. **Ariana** - Ville moderne proche de la capitale
10. **Médenine** - Porte du désert

### 4. Informations par Ville
Pour chaque ville, les informations suivantes sont affichées :
- **Description** : Caractéristiques principales
- **Population** : Nombre d'habitants
- **Enchères actives** : Nombre d'enchères en cours
- **Prochaines enchères** : Enchères à venir
- **Bureau des douanes** : Localisation du bureau

## Structure des Fichiers

### 1. Fichiers HTML
- `AM.UI.WEB/Views/Shared/_tunisia_map.cshtml` : Template principal de la carte

### 2. Fichiers CSS
- `AM.UI.WEB/wwwroot/css/tunisia-map.css` : Styles personnalisés pour la carte

### 3. Fichiers JavaScript
- `AM.UI.WEB/wwwroot/js/tunisia-map.js` : Logique interactive de la carte

### 4. Dépendances Externes
- **Leaflet CSS** : `https://unpkg.com/leaflet@1.9.4/dist/leaflet.css`
- **Leaflet JS** : `https://unpkg.com/leaflet@1.9.4/dist/leaflet.js`

## Intégration

### Modification dans Index.cshtml
```html
<!-- Ancien -->
@Html.Partial("_commnet_ca_marche")

<!-- Nouveau -->
@Html.Partial("_tunisia_map")
```

### Mise à jour des liens de navigation
```html
<!-- Ancien -->
<a href="#howitworks-section" class="smoothscroll arrow-down">

<!-- Nouveau -->
<a href="#tunisia-map-section" class="smoothscroll arrow-down">
```

## Fonctionnalités Techniques

### 1. Responsive Design
- **Desktop** : Carte 500px de hauteur
- **Tablette** : Carte 400px de hauteur
- **Mobile** : Carte 350px de hauteur, filtres empilés

### 2. Interactions Utilisateur
- **Clic sur marqueur** : Sélection de ville et affichage des informations
- **Survol marqueur** : Agrandissement visuel
- **Filtres** : Mise à jour dynamique des marqueurs
- **Popup** : Informations détaillées avec bouton de sélection

### 3. Animations
- **Transition de vue** : Animation fluide lors du changement de région
- **Apparition du panneau** : Animation fadeInUp pour les informations de ville
- **Effets de survol** : Transitions CSS pour les boutons et marqueurs

## Personnalisation

### Ajouter une Nouvelle Ville
1. Ouvrir `AM.UI.WEB/wwwroot/js/tunisia-map.js`
2. Ajouter un objet ville dans le tableau `cities` :
```javascript
{
    name: "Nouvelle Ville",
    lat: 35.0000,
    lng: 10.0000,
    region: "centre", // nord, centre, ou sud
    description: "Description de la ville",
    activeAuctions: "X enchères",
    upcomingAuctions: "Y prochaines",
    customsOffice: "Bureau des douanes de Nouvelle Ville",
    population: "Z habitants",
    gouvernorat: "Gouvernorat"
}
```

### Modifier les Couleurs
Dans `AM.UI.WEB/wwwroot/css/tunisia-map.css`, modifier :
```css
/* Couleur principale (orange) */
#f69314

/* Couleur de survol */
rgba(246, 147, 20, 0.3)
```

### Personnaliser les Popups
Dans `AM.UI.WEB/wwwroot/js/tunisia-map.js`, modifier la variable `popupContent`.

## Accessibilité

- **Navigation clavier** : Support complet
- **Focus visible** : Contours pour les éléments focusés
- **Contraste** : Respect des standards WCAG
- **Textes alternatifs** : Descriptions appropriées

## Performance

- **Chargement asynchrone** : Leaflet chargé depuis CDN
- **Optimisation mobile** : Styles adaptatifs
- **Gestion d'erreurs** : Vérification de la disponibilité de Leaflet

## Maintenance

### Mise à jour des Données
Les données des enchères peuvent être mises à jour en modifiant les propriétés `activeAuctions` et `upcomingAuctions` dans le fichier JavaScript.

### Mise à jour de Leaflet
Pour mettre à jour Leaflet, changer les URLs dans le template HTML :
```html
<link rel="stylesheet" href="https://unpkg.com/leaflet@VERSION/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@VERSION/dist/leaflet.js"></script>
```

## Support Navigateur

- **Chrome** : 60+
- **Firefox** : 55+
- **Safari** : 12+
- **Edge** : 79+
- **Mobile** : iOS Safari 12+, Chrome Mobile 60+

## Prochaines Améliorations

1. **Intégration API** : Connexion avec une API pour les données en temps réel
2. **Géolocalisation** : Détection automatique de la position utilisateur
3. **Recherche** : Barre de recherche pour trouver une ville
4. **Clustering** : Regroupement des marqueurs pour de meilleures performances
5. **Couches personnalisées** : Ajout de couches thématiques (routes, points d'intérêt)

## Contact

Pour toute question ou suggestion concernant cette fonctionnalité, veuillez contacter l'équipe de développement.
