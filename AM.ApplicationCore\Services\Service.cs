﻿using AM.ApplicationCore.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace AM.ApplicationCore.Services
{
    public class Service<TEntity> : IService<TEntity> where TEntity : class
    {

        private readonly IGenericRepository<TEntity> _repository;
        private readonly IUnitOfWork _unitOfWork;
        public Service(IUnitOfWork unitOfWork)
        {
            this._repository = unitOfWork.Repository<TEntity>();
            this._unitOfWork = unitOfWork;
        }
        public virtual void Add(TEntity entity)
        {
            _repository.Add(entity);
        }
        public virtual void Update(TEntity entity)
        {
            // Détacher d'abord toutes les entités du même type avec la même clé
            // pour éviter les problèmes de suivi d'entité
            var existingEntity = _repository.GetById(GetPrimaryKeyValues(entity));
            if (existingEntity != null)
            {
                _repository.Detach(existingEntity);
            }

            _repository.Update(entity);
        }

        private object[] GetPrimaryKeyValues(TEntity entity)
        {
            // Cette méthode suppose que la clé primaire est une propriété avec l'attribut [Key]
            // ou une propriété nommée "Id" ou "{TypeName}Id"
            var entityType = typeof(TEntity);
            var keyProperty = entityType.GetProperties()
                .FirstOrDefault(p => p.GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.KeyAttribute), true).Any())
                ?? entityType.GetProperty("Id")
                ?? entityType.GetProperty($"{entityType.Name}Id")
                ?? entityType.GetProperty("cin"); // Cas spécifique pour Employe/Serveur

            if (keyProperty != null)
            {
                return new[] { keyProperty.GetValue(entity) };
            }

            // Si aucune clé n'est trouvée, retourner un tableau vide
            return Array.Empty<object>();
        }
        public virtual void Delete(TEntity entity)
        {
            _repository.Delete(entity);
        }
        public virtual void Delete(Expression<Func<TEntity, bool>> where)
        {
            _repository.Delete(where);
        }
        public virtual TEntity GetById(params object[] id)
        {
            return _repository.GetById(id);
        }

        public virtual IEnumerable<TEntity> GetAll()
        {
            return _repository.GetAll();
        }
        public virtual IEnumerable<TEntity> GetMany(Expression<Func<TEntity, bool>> filter )
        {
            return _repository.GetMany(filter);
        }

        public virtual TEntity Get(Expression<Func<TEntity, bool>> where)
        {
             return _repository.Get(where);
        }

        public void Commit()
        {
            try
            {
                _unitOfWork.Save();
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}

