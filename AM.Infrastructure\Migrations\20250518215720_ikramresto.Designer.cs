﻿// <auto-generated />
using System;
using AM.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AM.Infrastructure.Migrations
{
    [DbContext(typeof(AMContext))]
    [Migration("20250518215720_ikramresto")]
    partial class ikramresto
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AM.ApplicationCore.Domain.Client", b =>
                {
                    b.Property<int>("clientId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("clientId"));

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Pointfidelite")
                        .HasColumnType("int");

                    b.Property<string>("Prenom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("adresse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("clientId");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Employe", b =>
                {
                    b.Property<string>("cin")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Restaurantkey")
                        .HasColumnType("int");

                    b.Property<int>("TypeEmploye")
                        .HasColumnType("int");

                    b.Property<string>("email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("numTel")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("salaire")
                        .HasColumnType("float");

                    b.HasKey("cin");

                    b.HasIndex("Restaurantkey");

                    b.ToTable("Employes");

                    b.HasDiscriminator<int>("TypeEmploye").HasValue(0);

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Reservation", b =>
                {
                    b.Property<int>("RestaurantFK")
                        .HasColumnType("int");

                    b.Property<int>("clientFk")
                        .HasColumnType("int");

                    b.Property<DateTime>("Date")
                        .HasColumnType("date");

                    b.Property<string>("id")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("statutReservationos")
                        .HasColumnType("bit");

                    b.Property<int>("table")
                        .HasColumnType("int");

                    b.HasKey("RestaurantFK", "clientFk", "Date");

                    b.HasIndex("clientFk");

                    b.ToTable("Reservations");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Restaurant", b =>
                {
                    b.Property<int>("id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("id"));

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("adresse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("type")
                        .HasColumnType("int");

                    b.HasKey("id");

                    b.ToTable("Restaurants");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Serveur", b =>
                {
                    b.HasBaseType("AM.ApplicationCore.Domain.Employe");

                    b.Property<int>("note")
                        .HasColumnType("int");

                    b.HasDiscriminator().HasValue(2);
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.chef", b =>
                {
                    b.HasBaseType("AM.ApplicationCore.Domain.Employe");

                    b.Property<string>("Specialite")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasDiscriminator().HasValue(1);
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Employe", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Restaurant", "Restaurant")
                        .WithMany("Employes")
                        .HasForeignKey("Restaurantkey")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Restaurant");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Reservation", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Restaurant", "Restaurant")
                        .WithMany("Reservations")
                        .HasForeignKey("RestaurantFK")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AM.ApplicationCore.Domain.Client", "Client")
                        .WithMany("Reservations")
                        .HasForeignKey("clientFk")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("Restaurant");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Client", b =>
                {
                    b.Navigation("Reservations");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Restaurant", b =>
                {
                    b.Navigation("Employes");

                    b.Navigation("Reservations");
                });
#pragma warning restore 612, 618
        }
    }
}
