    /*
    Flaticon icon font: Flaticon
    Creation date: 28/03/2019 08:10
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-house:before { content: "\f100"; }
.flaticon-coin:before { content: "\f101"; }
.flaticon-home:before { content: "\f102"; }
.flaticon-flat:before { content: "\f103"; }
.flaticon-location:before { content: "\f104"; }
.flaticon-mobile-phone:before { content: "\f105"; }
    
    $font-Flaticon-house: "\f100";
    $font-Flaticon-coin: "\f101";
    $font-Flaticon-home: "\f102";
    $font-Flaticon-flat: "\f103";
    $font-Flaticon-location: "\f104";
    $font-Flaticon-mobile-phone: "\f105";