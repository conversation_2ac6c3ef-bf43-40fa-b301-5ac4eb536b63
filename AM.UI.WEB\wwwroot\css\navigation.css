/* ===== NAVIGATION STYLES ===== */

/* Navbar styling */
.site-navbar {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.site-navbar .nav-link, 
.site-navbar .site-logo a {
    color: #ffffff !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.site-navbar .nav-link:hover, 
.site-navbar .site-logo a:hover {
    color: #4CAF50 !important;
    transform: translateY(-1px);
}

/* Dropdown hover effect */
.site-menu .dropdown:hover > .dropdown-menu {
    display: block !important;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.site-menu .dropdown-menu {
    display: none;
    position: absolute;
    left: 50%;
    top: 100%;
    transform: translateX(-50%) translateY(10px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    margin-top: 0.5rem;
    border: none;
    transition: all 0.3s ease;
}

/* Modern cities dropdown design */
.villes-dropdown {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px;
    width: 720px;
    min-width: 720px;
    max-width: 90vw;
    padding: 32px;
    box-sizing: border-box;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.villes-dropdown li {
    list-style: none;
    margin: 0;
    padding: 0;
}

.villes-dropdown .dropdown-item {
    display: block;
    padding: 14px 18px;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 14px;
    color: #2c3e50;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(76, 175, 80, 0.1);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.villes-dropdown .dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
    transition: left 0.5s ease;
}

.villes-dropdown .dropdown-item:hover::before {
    left: 100%;
}

.villes-dropdown .dropdown-item:hover {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: #ffffff;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 
        0 8px 25px rgba(76, 175, 80, 0.3),
        0 4px 12px rgba(76, 175, 80, 0.2);
    border-color: #4CAF50;
}

/* Responsive design */
@media screen and (max-width: 1200px) {
    .villes-dropdown {
        grid-template-columns: repeat(3, 1fr) !important;
        width: 600px;
        min-width: 600px;
        gap: 10px;
        padding: 28px;
    }

    .villes-dropdown .dropdown-item {
        padding: 12px 16px;
        font-size: 13px;
    }
}

@media screen and (max-width: 992px) {
    .villes-dropdown {
        grid-template-columns: repeat(2, 1fr) !important;
        width: 420px;
        min-width: 420px;
        gap: 8px;
        padding: 24px;
    }

    .villes-dropdown .dropdown-item {
        padding: 10px 14px;
        font-size: 12px;
    }
}

@media screen and (max-width: 768px) {
    .villes-dropdown {
        grid-template-columns: repeat(1, 1fr) !important;
        width: 280px;
        min-width: 280px;
        gap: 6px;
        padding: 20px;
    }
}

/* Additional navbar improvements */
.site-navbar .dropdown-toggle::after {
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.site-navbar .dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg);
}

.site-navbar .dropdown-menu {
    border: none;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Mobile menu toggle */
.site-menu-toggle {
    color: #ffffff !important;
    transition: all 0.3s ease;
}

.site-menu-toggle:hover {
    color: #4CAF50 !important;
}

/* Ensure proper z-index for dropdown */
.site-navigation {
    position: relative;
    z-index: 999;
}

/* Fix for Bootstrap conflicts */
.dropdown-menu.show {
    display: grid !important;
}

/* Smooth transitions for all interactive elements */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Hover effects for better UX */
.nav-link {
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: #4CAF50;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Logo hover effect */
.site-logo a {
    position: relative;
    display: inline-block;
}

.site-logo a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(76, 175, 80, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 4px;
}

.site-logo a:hover::before {
    opacity: 1;
}
