﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AM.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ikramresto : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Dossie<PERSON>");

            migrationBuilder.DropTable(
                name: "Avocats");

            migrationBuilder.DropTable(
                name: "Specialites");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Clients",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "CIN",
                table: "Clients");

            migrationBuilder.AddColumn<int>(
                name: "clientId",
                table: "Clients",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<int>(
                name: "Pointfidelite",
                table: "Clients",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "adresse",
                table: "Clients",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Clients",
                table: "Clients",
                column: "clientId");

            migrationBuilder.CreateTable(
                name: "Restaurants",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nom = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    adresse = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    type = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Restaurants", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "Employes",
                columns: table => new
                {
                    cin = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    nom = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    email = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    numTel = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    salaire = table.Column<double>(type: "float", nullable: false),
                    Restaurantkey = table.Column<int>(type: "int", nullable: false),
                    TypeEmploye = table.Column<int>(type: "int", nullable: false),
                    note = table.Column<int>(type: "int", nullable: true),
                    Specialite = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Employes", x => x.cin);
                    table.ForeignKey(
                        name: "FK_Employes_Restaurants_Restaurantkey",
                        column: x => x.Restaurantkey,
                        principalTable: "Restaurants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Reservations",
                columns: table => new
                {
                    RestaurantFK = table.Column<int>(type: "int", nullable: false),
                    clientFk = table.Column<int>(type: "int", nullable: false),
                    Date = table.Column<DateTime>(type: "date", nullable: false),
                    id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    table = table.Column<int>(type: "int", nullable: false),
                    statutReservationos = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Reservations", x => new { x.RestaurantFK, x.clientFk, x.Date });
                    table.ForeignKey(
                        name: "FK_Reservations_Clients_clientFk",
                        column: x => x.clientFk,
                        principalTable: "Clients",
                        principalColumn: "clientId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Reservations_Restaurants_RestaurantFK",
                        column: x => x.RestaurantFK,
                        principalTable: "Restaurants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Employes_Restaurantkey",
                table: "Employes",
                column: "Restaurantkey");

            migrationBuilder.CreateIndex(
                name: "IX_Reservations_clientFk",
                table: "Reservations",
                column: "clientFk");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Employes");

            migrationBuilder.DropTable(
                name: "Reservations");

            migrationBuilder.DropTable(
                name: "Restaurants");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Clients",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "clientId",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "Pointfidelite",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "adresse",
                table: "Clients");

            migrationBuilder.AddColumn<string>(
                name: "CIN",
                table: "Clients",
                type: "nvarchar(450)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Clients",
                table: "Clients",
                column: "CIN");

            migrationBuilder.CreateTable(
                name: "Specialites",
                columns: table => new
                {
                    SpecialiteId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NomSpecialite = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Specialites", x => x.SpecialiteId);
                });

            migrationBuilder.CreateTable(
                name: "Avocats",
                columns: table => new
                {
                    AvocatId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    SpecialiteFK = table.Column<string>(type: "nvarchar(450)", nullable: true),
                    DateEmbauche = table.Column<DateTime>(type: "date", nullable: false),
                    Nom = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Prenom = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Avocats", x => x.AvocatId);
                    table.ForeignKey(
                        name: "FK_Avocats_Specialites_SpecialiteFK",
                        column: x => x.SpecialiteFK,
                        principalTable: "Specialites",
                        principalColumn: "SpecialiteId");
                });

            migrationBuilder.CreateTable(
                name: "Dossiers",
                columns: table => new
                {
                    AvocatId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    ClientId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    DateDepot = table.Column<DateTime>(type: "date", nullable: false),
                    Clos = table.Column<bool>(type: "bit", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Frais = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Dossiers", x => new { x.AvocatId, x.ClientId, x.DateDepot });
                    table.ForeignKey(
                        name: "FK_Dossiers_Avocats_AvocatId",
                        column: x => x.AvocatId,
                        principalTable: "Avocats",
                        principalColumn: "AvocatId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Dossiers_Clients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "Clients",
                        principalColumn: "CIN",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Avocats_SpecialiteFK",
                table: "Avocats",
                column: "SpecialiteFK");

            migrationBuilder.CreateIndex(
                name: "IX_Dossiers_ClientId",
                table: "Dossiers",
                column: "ClientId");
        }
    }
}
