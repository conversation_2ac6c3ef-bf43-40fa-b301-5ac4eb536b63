﻿// <auto-generated />
using System;
using AM.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AM.Infrastructure.Migrations
{
    [DbContext(typeof(AMContext))]
    [Migration("20250427045937_avocat")]
    partial class avocat
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AM.ApplicationCore.Domain.Avocat", b =>
                {
                    b.Property<string>("AvocatId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateEmbauche")
                        .HasColumnType("date");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Prenom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpecialiteFK")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("AvocatId");

                    b.HasIndex("SpecialiteFK");

                    b.ToTable("Avocats");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Client", b =>
                {
                    b.Property<string>("CIN")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Prenom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CIN");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Dossier", b =>
                {
                    b.Property<string>("AvocatId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ClientId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateDepot")
                        .HasColumnType("date");

                    b.Property<bool>("Clos")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Frais")
                        .HasColumnType("float");

                    b.HasKey("AvocatId", "ClientId", "DateDepot");

                    b.HasIndex("ClientId");

                    b.ToTable("Dossiers");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Specialite", b =>
                {
                    b.Property<string>("SpecialiteId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NomSpecialite")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("SpecialiteId");

                    b.ToTable("Specialites");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Avocat", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Specialite", "Specialite")
                        .WithMany("Avocats")
                        .HasForeignKey("SpecialiteFK");

                    b.Navigation("Specialite");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Dossier", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Avocat", "Avocat")
                        .WithMany("Dossiers")
                        .HasForeignKey("AvocatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AM.ApplicationCore.Domain.Client", "Client")
                        .WithMany("Dossiers")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Avocat");

                    b.Navigation("Client");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Avocat", b =>
                {
                    b.Navigation("Dossiers");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Client", b =>
                {
                    b.Navigation("Dossiers");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Specialite", b =>
                {
                    b.Navigation("Avocats");
                });
#pragma warning restore 612, 618
        }
    }
}
