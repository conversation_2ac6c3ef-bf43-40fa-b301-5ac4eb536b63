﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.2" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AM.ApplicationCore\AM.ApplicationCore.csproj" />
    <ProjectReference Include="..\AM.Infrastructure\AM.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\uploads\" />
  </ItemGroup>

</Project>
